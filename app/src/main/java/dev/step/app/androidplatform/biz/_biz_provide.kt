package dev.step.app.androidplatform.biz

import android.content.Context
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import dev.step.app.data.kvstore.*


@Module
class BizModule {

    @Single
    fun provideSplashAdHelper() = SplashHelper()

    @Single
    fun provideNotificationPermissionRequester(splashHelper: SplashHelper) = NotificationPermissionRequester(splashHelper)

    @Single
    fun provideTenjinHelper(
        userOperateDataKv: UserOperateDataKv
    ) = TenjinHelper(userOperateDataKv)

    @Single
    fun provideFirebaseRemoteConfigHelper() = FirebaseRemoteConfigHelper()

    @Single
    fun provideMaxRewardedAdHelper(
        context: Context,
        splashHelper: SplashHelper,
        userOperateDataKv: UserOperateDataKv,
    ) = MaxRewardedAdHelper(context, splashHelper, userOperateDataKv)

//    @Single
//    fun provideMaxMRecAdHelper(
//        splashHelper: SplashHelper,
//    ) = MaxMRecAdHelper(splashHelper)

    @Single
    fun provideRatingHelper(
        context: Context,
        userOperateDataKv: UserOperateDataKv,
    ) = RatingHelper(context, userOperateDataKv)

//    @Single
//    fun provideMaxInterstitialAdHelper(
//        splashHelper: SplashHelper,
//        remoteConfigHelper: FirebaseRemoteConfigHelper,
//        userDataStore: UserOperateDataKv,
//    ) = MaxInterstitialAdHelper(splashHelper, remoteConfigHelper, userDataStore)

//    @Single
//    fun provideMaxAppOpenAdHelper(
//        splashHelper: SplashHelper
//    ) = MaxAppOpenAdHelper(splashHelper)
//
//    @Single
//    fun provideBigoAppOpenAdHelper(
//        context: Context,
//        splashHelper: SplashHelper
//    ) = BigoAppOpenAdHelper(context, splashHelper)
}