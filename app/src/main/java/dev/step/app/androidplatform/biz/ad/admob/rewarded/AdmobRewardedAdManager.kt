package dev.step.app.androidplatform.biz.ad.admob.rewarded

import android.app.Activity
import android.content.Context
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.admob.AdmobAdUnitIds
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedLoadingDialogEvent
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogEventFlow
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val AD_CACHE_PERIOD_SECONDS = 45 * 60L
private const val AD_LOADING_TIMEOUT_SECONDS = 20

@Single
class AdmobRewardedAdManager(
    private val splashController: SplashHelper,
    private val userOperateDataKv: UserOperateDataKv,
) {

    @Suppress("PrivatePropertyName")
    private val TAG = "RewardedAdManager"

    private val adKey = AdmobAdUnitIds.REWARDED

    private var rewardedAd: RewardedAd? = null
    private var isLoadingAd = false
    private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

    private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

    sealed interface AdLoadingStateEvent {
        data object TimeOut : AdLoadingStateEvent
        data object Loaded : AdLoadingStateEvent
        data object FailedToLoad : AdLoadingStateEvent
    }

    sealed interface AdShowStateEvent {
        data object Finish : AdShowStateEvent
        data object Showing : AdShowStateEvent
        data object FailedToShow : AdShowStateEvent
    }

    val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AdShowStateEvent>()
    val adEarnedRewardEventFlow = EventFlow<Boolean>()
    private val adEarnedRewardStateFlow = MutableStateFlow(false)

    private var isShowAdWithTipsDialog = false
    private val currentActiveAdFrom = MutableStateFlow("")

    private var sendLoadingTimeOutJob: Job? = null

    fun tryToLoadAd(activity: Activity) {
        debugLog(tag = TAG) { "tryToLoadAd" }

        if (isLoadingAd) return

        if (isAdAvailable()) {
            debugLog(tag = TAG) { "hasAdAvailable" }
        } else {
            debugLog(tag = TAG) { "noAdAvailable" }

            loadAd(activity)
        }
    }

    private fun loadAd(context: Context) {
        sendLoadingTimeOutJob?.cancel()
        sendLoadingTimeOutJob = null
        sendLoadingTimeOutJob = GlobalScope.launch(Dispatchers.Default) {
//            delay(interstitialAdConfig.adLoadingTimeoutSeconds.toDuration(DurationUnit.SECONDS))
            delay(AD_LOADING_TIMEOUT_SECONDS.toDuration(DurationUnit.SECONDS))
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
            adLoadingStateEventFlow.send(AdLoadingStateEvent.TimeOut)
        }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
            return
        }

        debugLog(tag = TAG) { "loadAd" }

        isLoadingAd = true

        RewardedAd.load(
            context,
            adKey,
            AdRequest.Builder().build(),
            object : RewardedAdLoadCallback() {
                override fun onAdLoaded(ad: RewardedAd) {
                    debugLog(tag = TAG) { "onAdLoaded" }

                    ad.onPaidEventListener = OnPaidEventListener { adValue ->
                        val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
                        val adFormat = "rewarded"
                        val adUnitId = ad.adUnitId

                        AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
                        AnalyticsLogEvent.recordAdImpressionRevenue(
                            adValue,
                            adSourceName,
                            adFormat,
                            latestActiveAdPlaceNameFlow.value ?: ""
                        )
                        AnalyticsLogEvent.recordAdImpression(
                            adValue,
                            adSourceName,
                            adFormat,
                            adUnitId
                        )
                        AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, ad)
                    }

                    rewardedAd = ad
                    isLoadingAd = false
                    latestLoadAdSuccessInstant = nowInstant()

                    sendLoadingTimeOutJob?.cancel()
                    sendLoadingTimeOutJob = null
                    adLoadingStateEventFlow.send(AdLoadingStateEvent.Loaded)
                    debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

//                    logEventRecord("ad_${adUnitNameLowercase}_load_success")
                    logEventRecord("ad_rewarded_load_success")
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    debugLog(tag = TAG) { "onAdFailedToLoad" }

                    isLoadingAd = false

                    sendLoadingTimeOutJob?.cancel()
                    sendLoadingTimeOutJob = null
                    adLoadingStateEventFlow.send(AdLoadingStateEvent.FailedToLoad)
                }
            }
        )

        logEventRecord("ad_rewarded_load")
    }

    fun isAdAvailable(): Boolean {
        return rewardedAd != null && checkAdIsValidAtCachePeriod()
    }

    private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
        val secondsDifference: Long =
            nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
        return secondsDifference < adCachePeriodSeconds
    }

    private fun showAd(activity: Activity) {
        debugLog(tag = TAG) { "showAd" }

        logEventRecord("ad_rewarded_show")

        rewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                rewardedAd = null
                loadAd(activity)
                adShowStateEventFlow.send(AdShowStateEvent.Finish)
                adEarnedRewardEventFlow.send(adEarnedRewardStateFlow.value)
                adEarnedRewardStateFlow.update { false }
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
                rewardedAd = null
                loadAd(activity)
                adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
                adEarnedRewardEventFlow.send(false)
                adEarnedRewardStateFlow.update { false }
            }

            override fun onAdShowedFullScreenContent() {
                adShowStateEventFlow.send(AdShowStateEvent.Showing)
                debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
            }

            override fun onAdClicked() {
                splashController.doSkipSplash(true)
                logEventRecord("ad_rewarded_click")
            }

            override fun onAdImpression() {
                logEventRecord("ad_rewarded_impress")
            }
        }

        rewardedAd?.show(activity) {
            adEarnedRewardStateFlow.update { true }
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
    ) = withContext(Dispatchers.Main.immediate) {
        debugLog(tag = TAG) { "tryToShowAd" }

        adEarnedRewardStateFlow.update { false }

        adPlaceName?.let {
            latestActiveAdPlaceNameFlow.update { "rewarded_$adPlaceName" }
        }

        if (isAdAvailable()) { // cache available
            debugLog(tag = TAG) { "cache available" }
            delay(1_000)
            showAd(activity)
        } else { // cache not available
            debugLog(tag = TAG) { "cache not available" }
            loadAd(activity)
        }
    }

}
