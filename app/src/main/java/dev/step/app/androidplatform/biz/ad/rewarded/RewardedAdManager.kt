package dev.step.app.androidplatform.biz.ad.rewarded

import android.app.Activity
import android.content.Context
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.admob.rewarded.AdmobRewardedAdManager
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import org.koin.core.annotation.Single

@Single
class RewardedAdManager(
    private val context: Context,
    private val splashHelper: SplashHelper,
    private val userOperateDataKv: UserOperateDataKv,
    private val admobRewardedAdManager: AdmobRewardedAdManager,
) {

    @Suppress("PrivatePropertyName")
    private val TAG = "RewardedAdManager"

    private var isShowAdWithTipsDialog = false
    private val currentActiveAdFrom = MutableStateFlow("")
    private var tryToShowRewardedAdWithTipsDialogTimeoutJob: Job? = null

    init {
        // 监听AdmobRewardedAdManager的事件并转换为原有的事件流
        setupEventFlowAdapters()
    }

    private fun setupEventFlowAdapters() {
        GlobalScope.launch(Dispatchers.Main) {
            // 监听广告加载状态
            admobRewardedAdManager.adLoadingStateEventFlow.collect { event ->
                when (event) {
                    AdmobRewardedAdManager.AdLoadingStateEvent.Loaded -> {
                        debugLog(tag = TAG) { "AdmobRewardedAdManager.AdLoadingStateEvent.Loaded" }
                        
                        if (isShowAdWithTipsDialog) {
                            debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog) onAdLoaded" }
                            
                            tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
                            rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
                        }
                    }
                    
                    AdmobRewardedAdManager.AdLoadingStateEvent.FailedToLoad -> {
                        debugLog(tag = TAG) { "AdmobRewardedAdManager.AdLoadingStateEvent.FailedToLoad" }
                        
                        val adFrom = currentActiveAdFrom.first()
                        if (adFrom.isNotEmpty()) {
                            debugLog(tag = TAG) { "ad_${adFrom}_incentive_load_failed" }
                            logEventRecord("ad_${adFrom}_incentive_load_failed")
                        }
                        currentActiveAdFrom.update { "" }
                    }
                    
                    AdmobRewardedAdManager.AdLoadingStateEvent.TimeOut -> {
                        debugLog(tag = TAG) { "AdmobRewardedAdManager.AdLoadingStateEvent.TimeOut" }
                        rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)
                    }
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // 监听广告展示状态
            admobRewardedAdManager.adShowStateEventFlow.collect { event ->
                when (event) {
                    AdmobRewardedAdManager.AdShowStateEvent.Showing -> {
                        debugLog(tag = TAG) { "AdmobRewardedAdManager.AdShowStateEvent.Showing" }
                        
                        splashHelper.doSkipSplash(true)
                        
                        val adFrom = currentActiveAdFrom.first()
                        if (adFrom.isNotEmpty()) {
                            debugLog(tag = TAG) { "ad_${adFrom}_incentive_impress" }
                            logEventRecord("ad_${adFrom}_incentive_impress")
                            logEventRecord("ad_incentive_impress")
                        }
                    }
                    
                    AdmobRewardedAdManager.AdShowStateEvent.Finish -> {
                        debugLog(tag = TAG) { "AdmobRewardedAdManager.AdShowStateEvent.Finish" }
                        
                        val adFrom = currentActiveAdFrom.first()
                        debugLog(tag = TAG) { "ad_${adFrom}_incentive_close" }
                        logEventRecord("ad_${adFrom}_incentive_close")
                        
                        if (isShowAdWithTipsDialog) {
                            rewardedLoadingDialogFinishEventFlow.send(Unit)
                            isShowAdWithTipsDialog = false
                        }
                    }
                    
                    AdmobRewardedAdManager.AdShowStateEvent.FailedToShow -> {
                        debugLog(tag = TAG) { "AdmobRewardedAdManager.AdShowStateEvent.FailedToShow" }
                        
                        val adFrom = currentActiveAdFrom.first()
                        if (adFrom.isNotEmpty()) {
                            debugLog(tag = TAG) { "ad_${adFrom}_incentive_display_failed" }
                            logEventRecord("ad_${adFrom}_incentive_display_failed")
                        }
                    }
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // 监听广告奖励状态
            admobRewardedAdManager.adEarnedRewardEventFlow.collect { earned ->
                if (earned) {
                    debugLog(tag = TAG) { "AdmobRewardedAdManager.adEarnedRewardEventFlow: earned reward" }
                    
                    val adFrom = currentActiveAdFrom.first()
                    debugLog(tag = TAG) { "ad_${adFrom}_incentive_rewarded" }
                    logEventRecord("ad_${adFrom}_incentive_rewarded")
                }
            }
        }
    }

    fun hasInit(): Boolean {
        // AdmobRewardedAdManager 不需要显式初始化
        return true
    }

    fun initIfNeed(activity: Activity) {
        // AdmobRewardedAdManager 不需要显式初始化，但我们可以预加载广告
        admobRewardedAdManager.tryToLoadAd(activity)
    }

    fun showRewardedAd() {
        GlobalScope.launch(Dispatchers.Main) {
            val activity = dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
            if (activity != null) {
                val adFrom = currentActiveAdFrom.first()
                admobRewardedAdManager.tryToShowAd(activity, adFrom)
            }
        }
    }

    fun tryToLoadRewardedAd() {
        val activity = dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
        if (activity != null) {
            admobRewardedAdManager.tryToLoadAd(activity)
        }
    }

    fun tryToShowRewardedLoadingDialog(
        from: String? = null,
        instantlyLoad: Boolean = !userOperateDataKv.tenjinAttr.isOrganic()
    ) {
        debugLog(tag = TAG) { "tryToShowRewardedLoadingDialog from: $from" }

        GlobalScope.launch(Dispatchers.Main) {
            isShowAdWithTipsDialog = true

            debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.StartShow)" }
            rewardedLoadingDialogEventFlow.send(
                RewardedLoadingDialogEvent.StartShow(instantlyLoad)
            )

            from?.let {
                debugLog(tag = TAG) { "ad_${from}_incentive_show" }
                logEventRecord("ad_${from}_incentive_show")
                logEventRecord("ad_incentive_show")
                currentActiveAdFrom.emit(from)
            }

            val activity = dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
            if (activity != null) {
                // 检查是否有可用的广告缓存
                if (admobRewardedAdManager.isAdAvailable()) {
//                    delay(1000)
                    debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)" }
                    rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
                } else {
                    // 没有缓存，开始加载广告
                    admobRewardedAdManager.tryToLoadAd(activity)

                    // 设置超时处理
                    tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
                    tryToShowRewardedAdWithTipsDialogTimeoutJob = null
                    tryToShowRewardedAdWithTipsDialogTimeoutJob = launch {
                        delay(20_000) // 20秒超时
                        debugLog(tag = TAG) { "rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)" }
                        rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)
                    }
                }
            }
        }
    }
}
