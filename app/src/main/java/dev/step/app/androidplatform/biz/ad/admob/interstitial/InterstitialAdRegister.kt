package dev.step.app.androidplatform.biz.ad.admob.interstitial

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import dev.step.app.HomeNode
import dev.step.app.NavigateAction
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.previousKey
import dev.step.app.ui.dialog.adloading.InterAdLoadingDialog
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

typealias OnTryToShowInterAdAndNavAction = (NavigateAction) -> Unit
typealias OnBackAction = () -> Unit

@Composable
fun interstitialAdRegister(
    navigator: Navigator,
    checkBackToHome: Boolean = true
): Pair<OnTryToShowInterAdAndNavAction, OnBackAction> {
    val context = LocalContext.current

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.collectSideEffect {
        when (it) {
            is AdmobInterstitialAdSideEffect.NavTo -> {
                it.navAction(navigator)
            }

            AdmobInterstitialAdSideEffect.NavUp -> {
                navigator.pop()
            }
        }
    }

    if (admobInterstitialAdViewState.adLoading) {
        InterAdLoadingDialog()
    }

    val onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction = { navAction ->
        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
            activity = context.findActivity(),
            navAction = navAction
        )
    }

    val onBackAction = {
        if (navigator.previousKey is HomeNode || !checkBackToHome) {
            admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(context.findActivity())
        } else {
            navigator.pop()
        }
        Unit
    }

    return onTryToShowInterAdAndNavAction to onBackAction
}
