//@file:Suppress("ObjectPropertyName")
//
//package dev.step.app.androidplatform.biz.ad.interstitial
//
//import android.app.Activity
//import com.applovin.mediation.MaxAd
//import com.applovin.mediation.MaxAdListener
//import com.applovin.mediation.MaxAdRevenueListener
//import com.applovin.mediation.MaxError
//import com.applovin.mediation.ads.MaxInterstitialAd
//import com.google.firebase.analytics.FirebaseAnalytics
//import com.roudikk.guia.extensions.currentKey
//import com.roudikk.guia.extensions.pop
//import com.roudikk.guia.extensions.removeAll
//import dev.step.app.BuildConfig
//import dev.step.app.DoGlobalNavigate
//import dev.step.app.InterAdLoadingDialogNode
//import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
//import dev.step.app.androidplatform.biz.SplashHelper
//import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
//import dev.step.app.androidplatform.biz.analytics.logEventAdRevenueRecord
//import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.ext.time.nowInstant
//import dev.step.app.data.kvstore.UserOperateDataKv
//import dev.step.app.sendGlobalNavigateEvent
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.GlobalScope
//import kotlinx.coroutines.Job
//import kotlinx.coroutines.async
//import kotlinx.coroutines.delay
//import kotlinx.coroutines.flow.*
//import kotlinx.coroutines.launch
//
//private const val TAG = "MaxInterstitialAdHelper"
//
//private const val _adReloadIntervalSeconds = 55 * 60
//
//sealed interface MaxInterstitialAdShowEvent {
//    data object TryToShowing : MaxInterstitialAdShowEvent
//    data object Showing : MaxInterstitialAdShowEvent
//    data object Skip : MaxInterstitialAdShowEvent
//    data object Hidden : MaxInterstitialAdShowEvent
//}
//
//class MaxInterstitialAdHelper(
//    private val splashHelper: SplashHelper,
//    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
//    private val userDataStore: UserOperateDataKv,
//) {
//    private var _interstitialAd: MaxInterstitialAd? = null
//
//    private val _lastCacheAdInstantSeconds = MutableStateFlow(0L)
//    private val _lastShowAdInstantSeconds = MutableStateFlow(0L)
//    private val _isLoadingAdFlow = MutableStateFlow(false)
//    private val _currentActiveAdFrom = MutableStateFlow("")
//
//    private val _interstitialAdEventChannel = MutableSharedFlow<MaxInterstitialAdShowEvent>(0)
//    val interstitialAdEventFlow: SharedFlow<MaxInterstitialAdShowEvent> get() = _interstitialAdEventChannel
//
//    private val maxInterstitialAdListener = object : MaxAdListener, MaxAdRevenueListener {
//        override fun onAdLoaded(ad: MaxAd) {
//            debugLog("$TAG onAdLoaded")
//
//            val now = nowInstant()
//
//            _isLoadingAdFlow.update { false }
//            _lastCacheAdInstantSeconds.update { now.epochSeconds }
//        }
//
//        override fun onAdDisplayed(ad: MaxAd) {
//            debugLog("$TAG onAdDisplayed")
//
//            val now = nowInstant()
//
//            splashHelper.doSkipSplash(true)
//
//            _lastShowAdInstantSeconds.update { now.epochSeconds }
//
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = _currentActiveAdFrom.first()
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_inter_impress")
//                    logEventRecord("ad_${adFrom}_inter_impress")
//                    logEventRecord("ad_inter_impress")
//                }
//
//                _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Showing)
//            }
//        }
//
//        override fun onAdHidden(ad: MaxAd) {
//            debugLog("$TAG onAdHidden")
//
//
//            GlobalScope.launch(Dispatchers.Main) {
//                _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Hidden)
//                val adFrom = _currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_inter_close")
//                    logEventRecord("ad_${adFrom}_inter_close")
//                }
//
//                _currentActiveAdFrom.update { "" }
//                loadInterstitialAd()
//            }
//        }
//
//        override fun onAdClicked(ad: MaxAd) {
//            debugLog("$TAG onAdClicked")
//
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = _currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_inter_click")
//                    logEventRecord("ad_${adFrom}_inter_click")
//                    logEventRecord("ad_inter_click")
//                }
//            }
//        }
//
//        override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
//            debugLog("$TAG onAdLoadFailed")
//
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = _currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_inter_load_failed")
//                    logEventRecord("ad_${adFrom}_inter_load_failed")
//                }
//
//                _currentActiveAdFrom.update { "" }
//                _isLoadingAdFlow.update { false }
//            }
//
//        }
//
//        override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = _currentActiveAdFrom.first()
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_inter_display_failed")
//                    logEventRecord("ad_${adFrom}_inter_display_failed")
//                }
//            }
//        }
//
//        override fun onAdRevenuePaid(ad: MaxAd) {
//            debugLog("$TAG onAdRevenuePaid")
//
//            if (!BuildConfig.DEBUG) {
//                ad?.let {
//                    logEventAdRevenueRecord("Ad_Impression_Revenue") {
//                        putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                        putString(FirebaseAnalytics.Param.CURRENCY, "USD")
//                        putString("adNetwork", ad.networkName)
//                        putString("adFormat", ad.format.label)
//                    }
//
//                    AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(
//                        adFormat = ad.format.label,
//                        adValue = ad.revenue,
//                        adNetwork = ad.networkName,
//                        adUnitId = ad.adUnitId
//                    )
//
//                    logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
//                        putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
//                        putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
//                        putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
//                        putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
//                        putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                        putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
//                    }
//
//                    AnalyticsLogEvent.roasReport("inter", ad)
//                }
//            }
//        }
//    }
//
//    private fun getAdShowIntervalSeconds(): Int {
//        val interstitialAdInterval = remoteConfigHelper.getInterstitialAdInterval()
//        return if (userDataStore.tenjinAttr.isOrganic()) {
//            interstitialAdInterval.for_o_user
//        } else {
//            interstitialAdInterval.for_p_user
//        }
//    }
//
//
//    fun initIfNeed(activity: Activity) {
//        if (_interstitialAd == null) {
//            debugLog("_interstitialAd init")
//            _interstitialAd = MaxInterstitialAd(BuildConfig.MAX_INTER_ID, activity).apply {
//                setListener(maxInterstitialAdListener)
//                setRevenueListener(maxInterstitialAdListener)
//            }
//            loadInterstitialAd()
//        }
//    }
//
//    private fun loadInterstitialAd() {
//        _interstitialAd?.let { it ->
//            debugLog("$TAG _interstitialAd?.let{}")
//            it.loadAd()
//            _isLoadingAdFlow.update { true }
//        }
//    }
//
//    private fun showInterstitialAd() {
//        _interstitialAd?.let { it ->
//            it.showAd()
//        }
//    }
//
//    fun tryToLoadAd() {
//        GlobalScope.launch(Dispatchers.Main) {
//            val now = nowInstant()
//
//            if (
//                _interstitialAd?.isReady == true
//                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
//            ) { // has available ad cache
//                debugLog("loadAd has available ad cache")
//            } else { // need load new ad
//                val isLoadingAd = _isLoadingAdFlow.first()
//
//                if (!isLoadingAd) {
//                    loadInterstitialAd()
//                }
//            }
//        }
//    }
//
//    private var collectOnAdShowOrHiddenJob: Job? = null
//    fun tryToShowAd(
//        from: String? = null,
//        onAdShowingOrSkip: (() -> Unit)? = null, // dialog use this
//        onAdHiddenOrSkip: (() -> Unit)? = null, // screen use this
//    ) {
//        debugLog("$TAG tryToShowAd from: $from")
//
//        GlobalScope.launch(Dispatchers.Main) {
//            collectOnAdShowOrHiddenJob?.cancel()
//            collectOnAdShowOrHiddenJob = null
//
//            sendGlobalNavigateEvent(DoGlobalNavigate.NavNode(InterAdLoadingDialogNode))
//            _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.TryToShowing)
//
//            val now = nowInstant()
//            if (
//                _interstitialAd?.isReady == true
//                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
//            ) { // has available ad cache
//
//                val adShowIntervalSeconds = getAdShowIntervalSeconds()
//
//                debugLog("$TAG adShowIntervalSeconds: $adShowIntervalSeconds")
//
//                if (_lastShowAdInstantSeconds.first() + adShowIntervalSeconds <= now.epochSeconds) {
//                    delay(1000)
//                    from?.let {
//                        debugLog("ad_${from}_inter_show")
//                        logEventRecord("ad_${from}_inter_show")
//                        logEventRecord("ad_inter_show")
//                        _currentActiveAdFrom.update { from }
//                    }
//                    showInterstitialAd()
//                } else {
//                    tryToPopInterAdDialog()
//                    onAdShowingOrSkip?.invoke()
//                    onAdHiddenOrSkip?.invoke()
//
//                    debugLog("do not need show InterstitialAd. from: $from")
//                    _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Skip)
//                }
//
//            } else {
//                tryToPopInterAdDialog()
//                onAdShowingOrSkip?.invoke()
//                onAdHiddenOrSkip?.invoke()
//
//                _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Skip)
//
//                val isLoadingAd = _isLoadingAdFlow.first()
//
//                if (!isLoadingAd) {
//                    loadInterstitialAd()
//                }
//            }
//
//            collectOnAdShowOrHiddenJob = GlobalScope.launch(Dispatchers.IO) {
//                _interstitialAdEventChannel.take(2).collect {
//                    when (it) {
//                        MaxInterstitialAdShowEvent.Showing -> {
//                            tryToPopInterAdDialog()
//                            if (onAdShowingOrSkip != null) {
//                                async(Dispatchers.Main) {
//                                    onAdShowingOrSkip()
//                                }
//                            }
//                        }
//
//                        MaxInterstitialAdShowEvent.Hidden -> {
//                            tryToPopInterAdDialog()
//                            if (onAdHiddenOrSkip != null) {
//                                async(Dispatchers.Main) {
//                                    onAdHiddenOrSkip()
//                                }
//                            }
//                        }
//
//                        else -> {}
//                    }
//                }
//            }
//        }
//    }
//
//    private suspend fun tryToPopInterAdDialog() {
//        sendGlobalNavigateEvent(DoGlobalNavigate.NavBlock {
//            if (currentKey is InterAdLoadingDialogNode) {
//                pop()
//            }
//            removeAll { it == InterAdLoadingDialogNode }
//        })
//    }
//}