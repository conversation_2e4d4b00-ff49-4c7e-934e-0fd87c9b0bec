@file:Suppress("ObjectPropertyName")

package dev.step.app

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.push
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.*
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.ui.screen.steps.backToStepsScreenDoNotShowInterAd
//import dev.step.app.ui.screen.wallet.walletScreenHasBeenInactiveFlow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.util.Locale
import kotlin.time.Duration.Companion.seconds
import kotlin.time.DurationUnit
import kotlin.time.toDuration

//fun Context.isMyServiceForeground(serviceClass: Class<*>): Boolean {
//    val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
//    val services = manager.getRunningServices(Integer.MAX_VALUE)
//    for (service in services) {
//        if (serviceClass.name == service.service.className) {
//            if (service.foreground && service.pid == android.os.Process.myPid()) {
//                // 如果该服务正在前台运行，则返回 true
//                return true
//            }
//        }
//    }
//    return false
//}

class App : Application() {

    override fun onCreate() {
        super.onCreate()
        GlobalScope.launch(Dispatchers.Main.immediate) {
            tryToStartStepTrack(0.seconds)
        }
        registerActivityLifecycleCallbacks(AppActivityLifecycleCallbacks)

//        GlobalScope.launch {
//            repeat(100) {
//                val myServiceRunning = isMyServiceForeground(ActiveStepTrackService::class.java)
//                debugLog("ActiveStepTrackService is foreground service: $myServiceRunning")
//                delay(2000)
//            }
//        }
    }
}


private data class MainActivityLifecycleCounter(
    var createdCounter: Int = 0,
    var startedCounter: Int = 0,
    var resumedCounter: Int = 0,
    var pausedCounter: Int = 0,
    var stoppedCounter: Int = 0,
    var saveInstanceStateCounter: Int = 0,
    var destroyedCounter: Int = 0,
)

private var _mainActivityLifecycleCounter = MainActivityLifecycleCounter()
private val _mainActivityClassName = MainActivity::class.java.name

object AppActivityLifecycleCallbacks : Application.ActivityLifecycleCallbacks, KoinComponent {

    private val tenjinHelper: TenjinHelper by inject()
    private val maxRewardedAdHelper: MaxRewardedAdHelper by inject()
    private val rewardedAdManager: RewardedAdManager by inject()
//    private val maxMRecAdHelper: MaxMRecAdHelper by inject()
//    private val maxInterstitialAdHelper: MaxInterstitialAdHelper by inject()
//    private val maxAppOpenAdHelper: MaxAppOpenAdHelper by inject()
    private val splashHelper: SplashHelper by inject()

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        if (_mainActivityClassName == activity.javaClass.name) {
            // 使用新的RewardedAdManager替代MaxRewardedAdHelper
            rewardedAdManager.initIfNeed(activity)
            // 保留MaxRewardedAdHelper的初始化以确保兼容性（可以后续移除）
            maxRewardedAdHelper.initIfNeed(activity)
//            maxMRecAdHelper.initIfNeed(activity)
//            maxInterstitialAdHelper.initIfNeed(activity)
//            if (Locale.getDefault().country != "RU") {
//                maxAppOpenAdHelper.initIfNeed(activity)
//            }

            _mainActivityLifecycleCounter.createdCounter++
        }
    }

    override fun onActivityStarted(activity: Activity) {
        if (_mainActivityClassName == activity.javaClass.name) {
            debugLog("AutoLaunchSplashAdScreenRegistrar _mainActivityLifecycleCounter: $_mainActivityLifecycleCounter")

            debugLog("test_tenjin try init")
            tenjinHelper.initAndStoreAttributionIfNeed(activity)

            if (_mainActivityLifecycleCounter.startedCounter > 0
                && _mainActivityLifecycleCounter.createdCounter > _mainActivityLifecycleCounter.destroyedCounter
            ) {
                GlobalScope.launch(Dispatchers.Main) {

                    runCatching {
                        val currentNode = globalNavigator?.currentKey

                        val currentDestinationIsSplashAd = currentNode is SplashNode

                        if (!currentDestinationIsSplashAd && !splashHelper.skipSplash.first()) {
                            debugLog("loadAd navigate to splashAd screen _mainActivityLifecycleCounter: $_mainActivityLifecycleCounter")

                            globalNavigator?.push(
                                SplashNode(isColdStart = false)
                            )
                        } else {
                            splashHelper.doSkipSplash(false)
                        }
                    }.onFailure {
                        it.printStackTrace()
                    }
                }
            }

            _mainActivityLifecycleCounter.startedCounter++
        }
    }

    override fun onActivityResumed(activity: Activity) {
        if (_mainActivityClassName == activity.javaClass.name) {
            if (_mainActivityLifecycleCounter.resumedCounter > 0) {
                GlobalScope.launch(Dispatchers.Main.immediate) {
                    tryToStartStepTrack(1.toDuration(DurationUnit.SECONDS))
                }
            }

            _mainActivityLifecycleCounter.resumedCounter++
        }
    }

    override fun onActivityPaused(activity: Activity) {
        if (_mainActivityClassName == activity.javaClass.name) {
            _mainActivityLifecycleCounter.pausedCounter++
        }
    }

    override fun onActivityStopped(activity: Activity) {
        if (_mainActivityClassName == activity.javaClass.name) {
            GlobalScope.launch {
                backToStepsScreenDoNotShowInterAd.emit(Unit)
//                walletScreenHasBeenInactiveFlow.emit(true)
            }
            _mainActivityLifecycleCounter.stoppedCounter++
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        if (_mainActivityClassName == activity.javaClass.name) {
            _mainActivityLifecycleCounter.saveInstanceStateCounter++
        }
    }

    override fun onActivityDestroyed(activity: Activity) {
        if (_mainActivityClassName == activity.javaClass.name) {
            _mainActivityLifecycleCounter = MainActivityLifecycleCounter()
        }
    }
}
