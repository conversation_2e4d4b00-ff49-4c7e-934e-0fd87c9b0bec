package dev.step.app.ui.screen.game1

import android.annotation.SuppressLint
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.Game1Node
import dev.step.app.GameNoRemainingTimeDialogNode
import dev.step.app.GameRewardedArgs
import dev.step.app.GameRewardedDialogNode
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.androidplatform.ext.navigationBarHeight
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.MyCoinsChip
import dev.step.app.ui.common.appluck.AppLuckItem
import dev.step.app.ui.common.appluck.AppLuckPlaceholder
import dev.step.app.ui.common.game.lkw.LuckyWheel
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import dev.step.app.ui.theme.noRippleClickable
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun Game1(
    args: Game1Node.Game1Args,
    navigator: Navigator? = null
) {
    val viewModel: Game1ViewModel = koinViewModel { parametersOf(args) }
    val viewState by viewModel.collectAsState()

    val playState = remember { mutableStateOf(false) }

    viewModel.collectSideEffect {
        when (it) {
            is Game1SideEffect.ToNoRemainingTimeDialog -> {
                navigator?.push(
                    GameNoRemainingTimeDialogNode(gameADT = GameADT.Game1)
                )
            }

            is Game1SideEffect.ToRewardedDialog -> {
                navigator?.push(
                    GameRewardedDialogNode(
                        args = GameRewardedArgs(
                            gameADT = GameADT.Game1,
                            coins = it.coins,
                            times = it.times
                        )
                    )
                )
            }

            is Game1SideEffect.DoPlay -> {
                playState.value = true
            }
        }
    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            Lifecycle.Event.ON_RESUME -> viewModel.onRefreshCoins()
            else -> {}
        }
    }

//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    val navUp = {
        if (!viewModel.isPlaying) {
            OnBack()
        }
    }

    NavBackHandler(onBack = navUp)

    Game1(
        navUp = navUp,
        viewModel = viewModel,
        viewState = viewState,
        playState = playState
    )
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun Game1(
    navUp: () -> Unit,
    viewModel: Game1ViewModel,
    viewState: Game1ViewState,
    playState: MutableState<Boolean>,
) {

    Scaffold {
        Box {
            Image(
                painter = painterResource(id = R.drawable.bg_game1),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.fillMaxSize()
            )
        }

        Column {
            Game1TopBar(
                walletCoins = viewState.walletCoins,
                withdrawEnable = viewState.withdrawEnable,
                navUp = navUp
            )

            Game1Content(
                viewModel = viewModel,
                viewState = viewState,
                playState = playState,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun Game1Content(
    viewModel: Game1ViewModel,
    viewState: Game1ViewState,
    playState: MutableState<Boolean>,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Spacer(modifier = Modifier.weight(1f))

        Image(
            painter = painterResource(id = R.drawable.img_game1_title),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .height(76.dp)
                .width(318.dp)
        )


        Box {
            AppLuckItem(
                placeholder = AppLuckPlaceholder.NO_2,
                modifier = Modifier.padding(top = 8.dp)
            )

            Column {
                BlankSpacer(height = 20.dp)

                LuckyWheel(
                    dataList = viewState.wheelItemsWithAward.map { it.data },
                    remaining = viewState.remaining.remainingTimes,
                    index = viewState.index,
                    rotateToIndex = viewState.rotateToIndex,
                    onPlay = viewModel::onPlay,
                    onRotateFinished = viewModel::onPlayFinish,
                    playState = playState,
                    enabled = viewState.enabled
                )
            }

        }


        Spacer(modifier = Modifier.weight(1f))

//        Image(
//            painter = painterResource(id = R.drawable.img_game1_btn),
//            contentDescription = null,
//            modifier = Modifier
//                .width(300.dp)
//                .noRippleClickable {
//                    viewModel.onPlay()
//                    if (viewState.enabled) {
//                        playState.value = true
//                    }
//                }
//        )

        Surface(
            onClick = {
                viewModel.onPlay()
                if (viewState.enabled) {
                    playState.value = true
                }
            },
            shape = CircleShape,
            color = Color(0xFFFBDBA3),
            modifier = Modifier.width(260.dp),
            elevation = 3.dp
        ) {
            Box(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = stringResource(id = R.string.text_play),
                    color = Color(0xFFFF6137),
                    fontSize = 25.sp,
                    modifier = Modifier
                        .padding(10.dp)
                        .align(Alignment.Center)
                )
            }
        }

        Spacer(modifier = Modifier.weight(1.9f))

        BlankSpacer(height = LocalContext.current.navigationBarHeight)
    }
}

@Composable
private fun Game1TopBar(
    walletCoins: Int?,
    withdrawEnable: Boolean,
    navUp: () -> Unit,
) {
    val context = LocalContext.current
    var coinIncrease by remember { mutableStateOf(false) }

    LaunchedEffect(coinIncrease) {
        if (coinIncrease) {
            delay(2500)
            coinIncrease = false
        }
    }

    Column {
        BlankSpacer(height = context.statusBarHeight)

        TopAppBar(
            title = {
                Box(
                    modifier = Modifier
                        .bodyWidth()
                        .padding(end = 16.dp)
                ) {
                    walletCoins?.let { _ ->
                        MyCoinsChip(
                            from = GameADT.Game1.gamePageName,
                            coins = walletCoins,
                            onIncrease = { coinIncrease = true },
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(horizontal = 16.dp),
                            color = Color.White,
                            withdrawEnable = withdrawEnable
                        )
                    }

//                if (coinIncrease) {
//                    CoinsAnimation(
//                        modifier = Modifier
//                            .size(60.dp)
//                            .padding(bottom = 8.dp, end = 3.dp)
//                            .align(Alignment.CenterEnd)
//                    )
//                }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.KeyboardArrowLeft,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = Color.White
                    )
                }
            },
            actions = {
                IconButton(onClick = navUp, enabled = false) {
                    Icon(
                        imageVector = Icons.Rounded.KeyboardArrowLeft,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = Color.Transparent
                    )
                }
            },
            backgroundColor = Color.Transparent,
            elevation = 0.dp
        )
    }
}

@Preview
@Composable
private fun Game1Preview() {
    AppTheme {
        Game1(
            args = Game1Node.Game1Args(),
            navigator = null,
        )
    }
}
