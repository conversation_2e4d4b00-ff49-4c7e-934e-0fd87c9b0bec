package dev.step.app.ui.screen.game1

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.Game1Node
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.common.game.lkw.LuckyWheelData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce
import org.orbitmvi.orbit.viewmodel.container
import kotlin.random.Random

@KoinViewModel
class Game1ViewModel(
    private val game1Args: Game1Node.Game1Args,
    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val admobInterstitialAdManager: AdmobInterstitialAdManager,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val walletBizKv: WalletBizKv,
    private val operateDataKv: UserOperateDataKv
) : ViewModel(), ContainerHost<Game1ViewState, Game1SideEffect> {

    override val container: Container<Game1ViewState, Game1SideEffect> =
        container(Game1ViewState.Empty)

    init {
        globalMainActivity?.let { activity ->
            admobInterstitialAdManager.tryToLoadAd(activity)
        }

        launchingAndPlay()
    }


    private fun launchingAndPlay() = intent {
        val navFromNotification = game1Args.navFromNotification

        val delayMillis = if (navFromNotification) 720L else 360L
        delay(delayMillis)

        runCatching {
            onPlay()
        }
    }

    fun onRefresh() = intent {
        val game1 = remoteConfigHelper.getGame1() ?: return@intent

        val limit = game1.play_times

        val todayStartSeconds = nowInstant().todayStartInstant().epochSeconds
        val remaining = walletBizKv.getGame1Remaining(todayStartSeconds)

        val a1Coins = game1.award1_coins
        val a2Coins = game1.award2_coins
        val a3Coins = game1.award3_coins
        val a4Coins = game1.award4_coins

        val a1p = game1.award1_percentage
        val a2p = game1.award2_percentage
        val a3p = game1.award3_percentage
        val a4p = game1.award4_percentage

        val wheelItemsWithAward = listOf(
            WheelItemWithAward(data = LuckyWheelData(a1Coins, R.drawable.img_lkw_coin_1), a1p),
            WheelItemWithAward(data = LuckyWheelData(a2Coins, R.drawable.img_lkw_coin_2), a2p),
            WheelItemWithAward(data = LuckyWheelData(a1Coins, R.drawable.img_lkw_coin_1), a1p),
            WheelItemWithAward(data = LuckyWheelData(a4Coins, R.drawable.img_lkw_coin_4), a4p),
            WheelItemWithAward(data = LuckyWheelData(a2Coins, R.drawable.img_lkw_coin_2), a2p),
            WheelItemWithAward(data = LuckyWheelData(a3Coins, R.drawable.img_lkw_coin_3), a3p),
        )

        val enabled = walletBizKv.getGame1Remaining(todayStartSeconds).remainingTimes > 0

        reduce {
            state.copy(
                remaining = remaining,
                remainingLimit = limit,
                wheelItemsWithAward = wheelItemsWithAward,
                enabled = enabled,
                withdrawEnable = !operateDataKv.tenjinAttr.isOrganic()
            )
        }
    }

    private var refreshTimes: Int = 0
    fun onRefreshCoins() = intent {
        if (refreshTimes > 0) {
            delay(1000)
            refreshTimes++
        }

        reduce {
            state.copy(
                walletCoins = walletBizKv.getCoinBalance()
            )
        }
    }

    var isPlaying: Boolean = false
    private var awardCoins: Int? = null
    fun onPlay() = intent {
        if (isPlaying) return@intent

        awardCoins = null

        val todayStartSeconds = nowInstant().todayStartInstant().epochSeconds

        if (walletBizKv.getGame1Remaining(todayStartSeconds).remainingTimes > 0) {
            logEventRecord("click_game1_start")

            val coins = doPlay(state)

            val index = randomPickItemFromCoins(state, coins)

            if (index != -1) {
                awardCoins = coins
                isPlaying = true
                reduce {
                    state.copy(
                        rotateToIndex = index
                    )
                }
                postSideEffect(Game1SideEffect.DoPlay)
            }
        } else {
            postSideEffect(Game1SideEffect.ToNoRemainingTimeDialog(GameADT.Game1))
        }
    }

    private var rewardedAdJob: Job? = null

    fun onPlayFinish(rotateToIndex: Int) = intent {
        reduce {
            state.copy(
                index = rotateToIndex
            )
        }
        isPlaying = false

        rewardedAdHelper.tryToShowRewardedLoadingDialog("game1_finish")

        viewModelScope.launch(Dispatchers.Main) {
            delay(1000)
            isPlaying = false
        }

        rewardedAdJob?.cancel()
        rewardedAdJob = null

        rewardedAdJob = viewModelScope.launch {
            rewardedLoadingDialogFinishEventFlow.take(1).collect {
                val awardTimes = remoteConfigHelper.getGame1()?.award_multiplier ?: 1

                awardCoins?.let {
                    postSideEffect(
                        Game1SideEffect.ToRewardedDialog(
                            GameADT.Game1,
                            it,
                            awardTimes
                        )
                    )
                    awardCoins = null
                }
                val todayStartSeconds = nowInstant().todayStartInstant().epochSeconds
                walletBizKv.spendOnceGame1()
                reduce {
                    state.copy(
                        remaining = walletBizKv.getGame1Remaining(todayStartSeconds),
                        enabled = walletBizKv.getGame1Remaining(todayStartSeconds).remainingTimes > 0
                    )
                }
            }
        }
    }

    private fun doPlay(state: Game1ViewState): Int? {
        val awards = mutableMapOf<Int, Int>()

        state.wheelItemsWithAward.sortedBy { it.percentage }.forEach {
            awards[it.percentage] = it.data.coins
        }

        val randomV = Random.nextInt(0, 100)

        debugLog("randomV: $randomV")

        awards.forEach { (p, coins) ->
            if (randomV <= p) {
                return coins
            }
        }

        return awards.values.minOrNull()
    }

    private fun randomPickItemFromCoins(state: Game1ViewState, coins: Int?): Int {
        val randomB = Random.nextBoolean()

        return if (randomB) {
            state.wheelItemsWithAward.indexOfFirst { item -> item.data.coins == coins }
        } else {
            state.wheelItemsWithAward.indexOfLast { item -> item.data.coins == coins }
        }
    }
}