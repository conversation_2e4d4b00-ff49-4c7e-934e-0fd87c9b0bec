package dev.step.app.ui.dialog.game

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.GameRewardedArgs
import dev.step.app.R
import dev.step.app.androidplatform.biz.RatingHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.ui.common.BigBadgeDialog
import dev.step.app.ui.common.BlankSpacer
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun GameRewardedDialog(
    args: GameRewardedArgs,
    navUp: () -> Unit,
    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    rewardedAdManager: dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager = koinInject(),
    ratingHelper: RatingHelper = koinInject(),
    viewModel: GameRewardedDialogViewModel = koinViewModel { parametersOf(args) }
) {
    val viewState by viewModel.collectAsState()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    viewModel.collectSideEffect {
        when (it) {
            is GameRewardedDialogSideEffect.NavUp -> {
                navUp()
                ratingHelper.tryToOpenReviewDialog()
            }
        }
    }

    val onClose = {
        viewModel.walletBizKv.setCoinBalance(
            viewModel.walletBizKv.getCoinBalance() + args.coins
        )

        OnBack()
        logEventRecord("exit_dialog_${args.gameADT.gamePageName}")
    }



    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = onClose,
            onClose = onClose,
            onConfirm = {
                viewModel.doClickGetCoinsMultiply()

                rewardedAdHelper.tryToShowRewardedLoadingDialog(args.gameADT.gamePageName)
                logEventRecord("click_${args.gameADT.gamePageName}_bonus")
            },
            confirmText = stringResource(id = R.string.title_get_coins),
            singleRewardTimes = args.times,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_rewarded),
            bigBadgeTitle = "+ ${args.coins} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = "${args.gameADT.gamePageName}_bonus"
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 26.dp)

                Text(text = stringResource(R.string.text_reward_), fontSize = 20.sp)

                BlankSpacer(height = 20.dp)
            }
        }
    }

}