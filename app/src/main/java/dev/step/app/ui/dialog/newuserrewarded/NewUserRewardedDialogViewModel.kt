package dev.step.app.ui.dialog.newuserrewarded

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.interstitial.MaxInterstitialAdHelper
import dev.step.app.androidplatform.biz.ad.interstitial.MaxInterstitialAdShowEvent
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce
import org.orbitmvi.orbit.viewmodel.container

data class NewUserRewardedDialogViewState(
    val hasClickGetCoinsMultiply: Boolean = false
) {
    companion object {
        val Empty = NewUserRewardedDialogViewState()
    }
}

sealed interface NewUserRewardedDialogSideEffect {
    data object NavUp : NewUserRewardedDialogSideEffect
}

@KoinViewModel
class NewUserRewardedDialogViewModel(
    adBizEnable: Boolean,
    remoteConfigHelper: FirebaseRemoteConfigHelper,
    admobInterstitialAdManager: AdmobInterstitialAdManager,
    walletBizKv: WalletBizKv,
) : ViewModel(), ContainerHost<NewUserRewardedDialogViewState, NewUserRewardedDialogSideEffect> {

    override val container: Container<NewUserRewardedDialogViewState, NewUserRewardedDialogSideEffect> =
        container(NewUserRewardedDialogViewState.Empty)

    init {

        if (!walletBizKv.isNewUserDialogHasShow()) {
            logEventRecord("show_dialog_newuser_first")
        } else {
            logEventRecord("show_dialog_newuser")
        }

        if (adBizEnable) {
            walletBizKv.setNewUserDialogHasShow(true)
        } else {
            walletBizKv.setNewUserState(false)
        }

        globalMainActivity?.let { activity ->
            admobInterstitialAdManager.tryToLoadAd(activity)
        }

        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        val dailyTask = remoteConfigHelper.getDailyTask()

        interstitialAdHelper.interstitialAdEventFlow.onEach { adShowEvent ->
            debugLog("_navUp MaxInterstitialAdShowEvent: $adShowEvent")
            if (adShowEvent is MaxInterstitialAdShowEvent.Skip || adShowEvent is MaxInterstitialAdShowEvent.Showing) {
                intent {
                    logEventRecord("exit_dialog_newuser")
                    postSideEffect(NewUserRewardedDialogSideEffect.NavUp)
                }
            }
        }.launchIn(viewModelScope)


        rewardedLoadingDialogFinishEventFlow.onEach {
            val addCoins = dailyTask?.new_user_rewarded_coins ?: 0

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            walletBizKv.setNewUserState(false)

            intent {
                postSideEffect(NewUserRewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)
    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}