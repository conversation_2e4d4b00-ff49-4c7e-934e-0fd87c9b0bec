package dev.step.app.ui.screen.steps

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.eventFlow
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import dev.step.app.MainActivity
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.theme.*
import org.orbitmvi.orbit.compose.collectAsState
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.biz.ActivityRecognitionPermissionRequester
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.findActivity
//import dev.step.app.androidplatform.androidcomponent.global.logEventRecord
//import dev.step.app.androidplatform.biz.MaxInterstitialAdHelper
//import dev.step.app.data.pojo.remoteconfig.RewardBubbles
//import dev.step.app.data.pojo.remoteconfig.RewardBubblesShowState
import dev.step.app.ui.common.*
import dev.step.app.ui.common.appluck.AppLuckItem
import dev.step.app.ui.common.appluck.AppLuckPlaceholder
import dev.step.app.ui.common.game.GameBanner
import dev.step.app.ui.common.task.TasksContent
import dev.step.app.ui.screen.permissionsmanager.ActivityRecognitionDialog
import dev.step.app.ui.screen.permissionsmanager.IgnoreBatteryOptimizationDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.runBlocking
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectSideEffect

val backToStepsScreenDoNotShowInterAd = MutableStateFlow<Unit?>(null)

@Composable
fun StepsScreenInHome(
    navigator: Navigator,
    openGoalStepChangeDialog: () -> Unit,
    openSignInRewardedDialog: () -> Unit,
    openExchangeCoinsRewardedDialog: () -> Unit,
    openNewUserRewardedDialog: (adBizEnable: Boolean) -> Unit,
    openNoEnoughStepsDialog: () -> Unit,
    openStageOneRewardedDialog: () -> Unit,
    openStageTwoRewardedDialog: () -> Unit,
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
    stepsViewModel: StepsViewModel,
) {
//    val scope = rememberCoroutineScope()

//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

//    OnLifecycleEvent { _, event ->
//        when (event) {
//            Lifecycle.Event.ON_START -> {
//                scope.launch {
//                    val doNotShowInterAd = backToStepsScreenDoNotShowInterAd.first() != null
//                    if (doNotShowInterAd) {
//                        debugLog("StepsScreen interstitialAd doNotShowInterAd")
//                        backToStepsScreenDoNotShowInterAd.emit(null)
//                    } else {
//                        debugLog("StepsScreen interstitialAd tryToShowAd")
//                        interstitialAdHelper.tryToShowAd("steps")
//                    }
//                }
//            }
//
//            else -> {}
//        }
//    }

    StepsScreen(
        navigator = navigator,
        openGoalStepChangeDialog = openGoalStepChangeDialog,
        openSignInRewardedDialog = openSignInRewardedDialog,
        openExchangeCoinsRewardedDialog = openExchangeCoinsRewardedDialog,
        openNewUserRewardedDialog = openNewUserRewardedDialog,
        openNoEnoughStepsDialog = openNoEnoughStepsDialog,
        openStageOneRewardedDialog = openStageOneRewardedDialog,
        openStageTwoRewardedDialog = openStageTwoRewardedDialog,
        openGame1 = openGame1,
        openGame2 = openGame2,
        openGame3 = openGame3,
        openRewardedDialog = openRewardedDialog,
        stepsViewModel = stepsViewModel,
    )
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun StepsScreen(
    navigator: Navigator,
    openGoalStepChangeDialog: () -> Unit,
    openSignInRewardedDialog: () -> Unit,
    openExchangeCoinsRewardedDialog: () -> Unit,
    openNewUserRewardedDialog: (adBizEnable: Boolean) -> Unit,
    openNoEnoughStepsDialog: () -> Unit,
    openStageOneRewardedDialog: () -> Unit,
    openStageTwoRewardedDialog: () -> Unit,
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
    stepsViewModel: StepsViewModel,
) {
    val context = LocalContext.current
    val splashHelper: SplashHelper = koinInject()

    val viewState by stepsViewModel.collectAsState()


    val lifecycleOwner = LocalLifecycleOwner.current
    var lifecycleEvent by remember { mutableStateOf<Lifecycle.Event?>(null) }

    var activityRecognitionPermissionDialogShowState: Boolean by remember {
        mutableStateOf(false)
    }

    val activityRecognitionPermissionState =
        if (stepsViewModel.stepTrackingUseStepDetectorSensor()) {
            ActivityRecognitionPermissionRequester.permissionState()
        } else {
            null
        }

    LaunchedEffect(activityRecognitionPermissionState, lifecycleEvent) {
        delay(500)
        val currentInDialog =
            navigator.currentKey?.tag()?.contains("dialog", ignoreCase = true) == true

        if (currentInDialog || MainActivity.windowFocusChangedFlow.first() != true) {

        } else if (activityRecognitionPermissionState == null) {
//            stepsViewModel.startTracking()
//            debugLog("LaunchedEffect stepsViewModel.startTrack() cuz api version < 29")
        } else if (!activityRecognitionPermissionState.allPermissionsGranted) {
            activityRecognitionPermissionDialogShowState = true
            debugLog("LaunchedEffect activityRecognitionPermissionState.launchPermissionRequest()")
        } else {
            activityRecognitionPermissionDialogShowState = false
//            stepsViewModel.startTracking()
//            debugLog("LaunchedEffect stepsViewModel.startTrack()")
        }
    }

    if (
        activityRecognitionPermissionDialogShowState
        && viewState.isNewUserDialogHasShow
        && ActivityRecognitionPermissionRequester.canOpenRequester(context.findActivity())
    ) {
        val currentInDialog =
            navigator.currentKey?.tag()?.contains("dialog", ignoreCase = true) == true

        if (!currentInDialog) {
//            PermissionsRequiredTipsDialog(
//                onDismiss = { permissionsRequiredTipsDialogShowState = false },
//                onConfirm = {
//                    permissionsRequiredTipsDialogShowState = false
//
//                    motionSensorPermissionState?.let {
//                        ActivityRecognitionPermissionRequester.launchPermissionRequest(
//                            context = context,
//                            motionSensorPermissionState = motionSensorPermissionState
//                        )
//                    }
//                }
//            )

            ActivityRecognitionDialog(
                onDismiss = { activityRecognitionPermissionDialogShowState = false },
                onRequest = {
                    activityRecognitionPermissionDialogShowState = false

                    activityRecognitionPermissionState?.let {
                        ActivityRecognitionPermissionRequester.launchPermissionRequest(
                            context = context,
                            motionSensorPermissionState = activityRecognitionPermissionState
                        )
                    }
                },
                isForceOpen = false
            )
        }
    }

    LaunchedEffect(Unit) {
        lifecycleOwner.lifecycle.eventFlow.onEach { event ->
            lifecycleEvent = event
            when (event) {
                Lifecycle.Event.ON_RESUME -> stepsViewModel.onRefresh(context)
                Lifecycle.Event.ON_STOP -> {
                    activityRecognitionPermissionDialogShowState = false
                }

                else -> {}
            }
        }.launchIn(this)
    }

    LaunchedEffect(lifecycleEvent) {
        delay(400)
        if (
            lifecycleEvent == Lifecycle.Event.ON_RESUME
            && MainActivity.windowFocusChangedFlow.first() == true
        ) {
            stepsViewModel.tryToAutoSignIn()
        }
    }

    debugLog("findActivity lifecycleEvent -> ${lifecycleEvent?.name.toString()}")

    LaunchedEffect(viewState.isNewUserDialogHasShow, lifecycleEvent) {
        delay(400)
        if (!viewState.isNewUserDialogHasShow) {
            if (MainActivity.windowFocusChangedFlow.first() == true) {
                stepsViewModel.onNewUserRewarded(true)
            }
        }
    }

    var ignoringBatteryOptimizationDialogShowState: Boolean by remember {
        mutableStateOf(false)
    }

    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()
    LaunchedEffect(Unit) {
        if (
            !activityRecognitionPermissionDialogShowState
            && activityRecognitionPermissionState?.allPermissionsGranted != false
            && ignoringBatteryOptimizationRequester.canOpenBatteryOptimizationSettings(context.findActivity())
        ) {
            ignoringBatteryOptimizationDialogShowState = true
        }
    }

    if (ignoringBatteryOptimizationDialogShowState) {
        IgnoreBatteryOptimizationDialog(
            onDismiss = { ignoringBatteryOptimizationDialogShowState = false },
            onRequest = {
                ignoringBatteryOptimizationRequester
                    .openSystemBatteryOptimizationSettings(context.findActivity())
                ignoringBatteryOptimizationDialogShowState = false
            },
            isForceOpen = false
        )
    }

    stepsViewModel.collectSideEffect {
        handleStepsSideEffect(
            it = it,
            openSignInRewardedDialog = openSignInRewardedDialog,
            openExchangeCoinsRewardedDialog = openExchangeCoinsRewardedDialog,
            openNewUserRewardedDialog = openNewUserRewardedDialog,
            openNoEnoughStepsDialog = openNoEnoughStepsDialog,
            openStageOneRewardedDialog = openStageOneRewardedDialog,
            openStageTwoRewardedDialog = openStageTwoRewardedDialog,
            openRewardedDialog = openRewardedDialog,
        )
    }


    Scaffold(modifier = Modifier.fillMaxSize(), backgroundColor = AppColor.BackgroundDefault) {
        StepsContent(
            viewModel = stepsViewModel,
            viewState = viewState,
            onNewUserRewarded = stepsViewModel::onNewUserRewarded,
//            onBubbleAdClick = stepsViewModel::onBubbleAdClick,
            openGoalStepChangeDialog = {
                openGoalStepChangeDialog()
                logEventRecord("click_step_edit")
            },
            openGame1 = {
                openGame1()
                logEventRecord("click_step_game1")
            },
            openGame2 = {
                openGame2()
                logEventRecord("click_step_game2")
            },
            openGame3 = {
                openGame3()
                logEventRecord("click_step_game3")
            },
        )
    }
}

@Composable
private fun StepsContent(
    viewModel: StepsViewModel,
    viewState: StepsViewState,
    onNewUserRewarded: (dialogAdBizEnable: Boolean) -> Unit,
//    onBubbleAdClick: (id: Int) -> Unit,
    openGoalStepChangeDialog: () -> Unit,
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    modifier: Modifier = Modifier,
) {
//    val statusBarHeight = LocalContext.current.statusBarHeight

    var coinIncrease by remember { mutableStateOf(false) }

    LaunchedEffect(coinIncrease) {
        if (coinIncrease) {
            delay(2500)
            coinIncrease = false
        }
    }

    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            TopHeadContent(
                viewModel = viewModel,
                viewState = viewState,
                openGoalStepChangeDialog = openGoalStepChangeDialog,
            )

            GameBanner(
                openGame1 = openGame1,
                openGame2 = openGame2,
                openGame3 = openGame3,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 12.dp)

            TasksContent(tasks = viewState.tasks, modifier = Modifier.fillMaxWidth())

            WeekStatistics(
                dayStepsDataList = viewState.daysOfWeekStepDataList,
                todayStepsData = viewState.todayStepsData,
                dailyStepGoal = viewState.stepsGoal,
                modifier = Modifier.padding(vertical = 12.dp, horizontal = 16.dp)
            )
        }
    }
}

@Composable
private fun TopHeadContent(
    viewModel: StepsViewModel,
    viewState: StepsViewState,
    openGoalStepChangeDialog: () -> Unit,
    modifier: Modifier = Modifier
) {
    val statusBarHeight = LocalContext.current.statusBarHeight

    Column(modifier) {
        BlankSpacer(height = statusBarHeight)

        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(id = R.string.steps_title),
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .weight(1f),
                color = AppColor.TextColorBlack,
                fontSize = 22.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.weight(1f))

            MyCoinsChip(
                from = "steps",
                coins = viewState.walletCoins ?: 0,
                onIncrease = {
//                coinIncrease = true
                },
                withdrawEnable = viewState.withdrawEnable
            )
        }

        Box {
            TodayStatistics(
                todayStepsData = viewState.todayStepsData,
                goalSteps = viewState.stepsGoal,
                distanceMi = viewState.distanceMi,
                distanceKm = viewState.distanceKm,
                kcal = viewState.kcal,
                duration = viewState.duration,
                mus = viewState.mus,
                openGoalStepChangeDialog = openGoalStepChangeDialog,
                onExchange = viewModel::onExchangeCoins,
                modifier = Modifier.padding(top = 22.dp, bottom = 12.dp)
            )

            FloatingBubbleAds(
                rewardBubblesShowState = viewState.rewardBubblesShowState,
                rewardedBubbles = viewState.rewardedBubbles,
                onBubbleAdClick = viewModel::onRewardedBubbleClick,
                modifier = Modifier.fillMaxWidth()
            )

            AppLuckItem(
                placeholder = AppLuckPlaceholder.NO_1,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 22.dp, bottom = 70.dp)
            )
        }
    }
}
