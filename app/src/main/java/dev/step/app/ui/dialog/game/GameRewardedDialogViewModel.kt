package dev.step.app.ui.dialog.game

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.GameRewardedArgs
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce
import org.orbitmvi.orbit.viewmodel.container

data class GameRewardedDialogViewState(
    val hasClickGetCoinsMultiply: Boolean = false
) {
    companion object {
        val Empty = GameRewardedDialogViewState()
    }
}

sealed interface GameRewardedDialogSideEffect {
    data object NavUp : GameRewardedDialogSideEffect
}

@KoinViewModel
class GameRewardedDialogViewModel(
    private val args: GameRewardedArgs,
    private val admobInterstitialAdManager: AdmobInterstitialAdManager,
    val walletBizKv: WalletBizKv,
) : ViewModel(), ContainerHost<GameRewardedDialogViewState, GameRewardedDialogSideEffect> {

    override val container: Container<GameRewardedDialogViewState, GameRewardedDialogSideEffect> =
        container(GameRewardedDialogViewState.Empty)

    private val gameADT = args.gameADT
    private val coins = args.coins
    private val times = args.times

    init {
        globalMainActivity?.let { activity ->
            admobInterstitialAdManager.tryToLoadAd(activity)
        }

        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        rewardedLoadingDialogFinishEventFlow.onEach {

            val addCoins = coins * times

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            intent {
                postSideEffect(GameRewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)

//        interstitialAdHelper.interstitialAdEventFlow.onEach { adShowEvent ->
//            if (adShowEvent is MaxInterstitialAdShowEvent.Skip || adShowEvent is MaxInterstitialAdShowEvent.Showing) {
//
//                walletBizKv.setCoinBalance(
//                    walletBizKv.getCoinBalance() + coins
//                )
//
//                intent {
//                    postSideEffect(GameRewardedDialogSideEffect.NavUp)
//                }
//                logEventRecord("exit_dialog_${gameADT.gamePageName}")
//            }
//        }.launchIn(viewModelScope)
    }

//    fun onClose() {
//        interstitialAdHelper.tryToShowAd("exit_dialog_${gameADT.gamePageName}")
//    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}