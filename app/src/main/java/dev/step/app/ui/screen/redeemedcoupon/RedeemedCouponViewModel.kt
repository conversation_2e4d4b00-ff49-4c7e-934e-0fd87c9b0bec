package dev.step.app.ui.screen.redeemedcoupon

import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.screen.withdraw.RedeemedPrize
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class RedeemedCouponViewModel(
    private val walletBizKv: WalletBizKv,
    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
) : ViewModel(), ContainerHost<RedeemedCouponViewState, RedeemedGiftCardSideEffect> {

    private var coinPrice: Int = 0

    private val emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}\$".toRegex()

    override val container: Container<RedeemedCouponViewState, RedeemedGiftCardSideEffect> =
        container(RedeemedCouponViewState.Empty)

    fun configure(
        coupon: RedeemedPrize.Coupon
    ) = intent {
        <EMAIL> = coupon.coinPrice

        reduce {
            state.copy(coupon = coupon)
        }
    }

    fun onEmailChange(emailTextField: TextFieldValue) = intent {
        reduce {
            state.copy(emailTextField = emailTextField)
        }
    }

    fun onMessageChange(messageTextField: TextFieldValue) = intent {
        reduce {
            state.copy(messageTextField = messageTextField)
        }
    }

    private var onRedeemNowJob: Job? = null
    fun onRedeemNow() = intent {
        val email  = state.emailTextField.text

        if (emailRegex.matches(email)) {
            onRedeemNowJob?.cancel()
            onRedeemNowJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("RedeemedGiftCard")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(RedeemedGiftCardSideEffect.Redeemed)

                    walletBizKv.setCoinBalance(walletBizKv.getCoinBalance() - coinPrice)

                    onRedeemNowJob?.cancel()
                    onRedeemNowJob = null
                }
            }
        } else {
            postSideEffect(RedeemedGiftCardSideEffect.EmailError)
        }
    }

    @Volatile
    var isEmailTextFieldClicked: Boolean = false

    @Volatile
    var isMessageTextFieldClicked: Boolean = false
}