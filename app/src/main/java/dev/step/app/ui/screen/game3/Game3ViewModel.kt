@file:Suppress("ObjectPropertyName")

package dev.step.app.ui.screen.game3

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.androidplatform.randomSelection
import dev.step.app.androidplatform.randomSort
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.dialog.game.gameNoRemainingNavUpEventFlow
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce
import org.orbitmvi.orbit.viewmodel.container
import kotlin.random.Random
import kotlin.random.nextInt

private val _scratchPatterns = listOf(
    ScratchPattern(R.drawable.img_sc_bule),
    ScratchPattern(R.drawable.img_sc_green),
    ScratchPattern(R.drawable.img_sc_orange),
    ScratchPattern(R.drawable.img_sc_red),
    ScratchPattern(R.drawable.img_sc_purple),
    ScratchPattern(R.drawable.img_sc_yellow)
)

@KoinViewModel
class Game3ViewModel(
    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
    private val admobInterstitialAdManager: AdmobInterstitialAdManager,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val walletBizKv: WalletBizKv,
    private val operateDataKv: UserOperateDataKv,
) : ViewModel(), ContainerHost<Game3ViewState, Game3SideEffect> {

    override val container: Container<Game3ViewState, Game3SideEffect> =
        container(Game3ViewState.Empty)

    init {
        gameNoRemainingNavUpEventFlow.onEach {
            intent {
                postSideEffect(Game3SideEffect.DisableScratchCard)
            }
        }.launchIn(viewModelScope)

        intent {
            postSideEffect(Game3SideEffect.ResetScratchCard)
        }

        globalMainActivity?.let {
            admobInterstitialAdManager.tryToLoadAd(it)
        }
    }

    fun onRefresh() = intent {
        val luckyScratch = remoteConfigHelper.getGame3()

        val playTimesLimit = luckyScratch?.play_times ?: 10

        val todayStartSeconds = nowInstant().todayStartInstant().epochSeconds
        val remaining =
            walletBizKv.getGame3Remaining(todayStartSeconds, playTimesLimit)

        reduce {
            state.copy(
                remaining = remaining,
                withdrawEnable = !operateDataKv.tenjinAttr.isOrganic(),
            )
        }
    }

    private var refreshTimes: Int = 0
    fun onRefreshCoins() = intent {
        if (refreshTimes > 0) {
            delay(1000)
            refreshTimes++
        }

        reduce {
            state.copy(
                walletCoins = walletBizKv.getCoinBalance()
            )
        }
    }

    private var rewardedAdJob: Job? = null
    fun onScratchCompleted() = intent {
        if (state.scratchState == ScratchState.Disable) return@intent

        val luckyScratch = remoteConfigHelper.getGame3()

        val coins = luckyScratch?.award_coins ?: 0
        val times = luckyScratch?.award_multiplier ?: 1

        reduce { state.copy(scratchState = ScratchState.Finish) }
//        delay(200)

        rewardedAdManager.tryToShowRewardedLoadingDialog("game3_finish")

        rewardedAdJob?.cancel()
        rewardedAdJob = null

        rewardedAdJob = viewModelScope.launch {
            rewardedLoadingDialogFinishEventFlow.take(1).collect {
                postSideEffect(Game3SideEffect.ToRewardedDialog(GameADT.Game3, coins, times))
//            delay(200)
                postSideEffect(Game3SideEffect.ResetScratchCard)
            }
        }
    }

    fun doSelectionScratchCard() = intent {

        val newPatternSelection = doPatternSelection()

        reduce {
            state.copy(
                scratchState = ScratchState.None,
                scratchPatterns = newPatternSelection
            )
        }
    }

    fun disableScratchCard() = intent {
        reduce { state.copy(scratchState = ScratchState.Disable) }
    }

    fun startScratch() = intent {
        if (state.scratchState == ScratchState.Doing) return@intent

        val now = nowInstant().todayStartInstant()
        val remainingLimit = remoteConfigHelper.getGame3()?.play_times ?: 10

        if (walletBizKv.getGame3Remaining(
                now.epochSeconds,
                remainingLimit
            ).remainingTimes > 0
        ) {
            logEventRecord("click_game3_start")

            walletBizKv.spendOnceGame3Remaining()
            reduce {
                state.copy(
                    scratchState = ScratchState.Doing,
                    remaining = walletBizKv.getGame3Remaining(
                        now.epochSeconds,
                        remainingLimit
                    )
                )
            }
        } else {
            postSideEffect(Game3SideEffect.ToNoRemainingTimeDialog(GameADT.Game3))
            postSideEffect(Game3SideEffect.DisableScratchCard)
        }

    }

    private fun doPatternSelection(): List<ScratchPattern> {
        val patternSelectionNum = Random.nextInt(2..4)

        val randomPatterns = _scratchPatterns.randomSelection(patternSelectionNum)

        val scratchPatternsResult = mutableListOf<ScratchPattern>()
        when (randomPatterns.size) {
            2 -> {
                val pattern0 = randomPatterns[0]
                val pattern1 = randomPatterns[1]

                repeat(4) { scratchPatternsResult.add(pattern0) }
                repeat(2) { scratchPatternsResult.add(pattern1) }
            }

            3 -> {
                val pattern0 = randomPatterns[0]
                val pattern1 = randomPatterns[1]
                val pattern2 = randomPatterns[2]

                val randomNi = Random.nextInt(0..1)

                if (randomNi == 0) { // use 3:2:1
                    repeat(3) { scratchPatternsResult.add(pattern0) }
                    repeat(2) { scratchPatternsResult.add(pattern1) }
                    repeat(1) { scratchPatternsResult.add(pattern2) }
                } else { // use 2:2:2
                    repeat(2) {
                        scratchPatternsResult.add(pattern0)
                        scratchPatternsResult.add(pattern1)
                        scratchPatternsResult.add(pattern2)
                    }
                }

            }

            4 -> {
                val pattern0 = randomPatterns[0]
                val pattern1 = randomPatterns[1]
                val pattern2 = randomPatterns[2]
                val pattern3 = randomPatterns[3]

                val randomNi = Random.nextInt(0..1)
                if (randomNi == 0) { // use 3:1:1:1
                    repeat(3) {
                        scratchPatternsResult.add(pattern0)
                    }
                    repeat(1) {
                        scratchPatternsResult.add(pattern1)
                        scratchPatternsResult.add(pattern2)
                        scratchPatternsResult.add(pattern3)
                    }
                } else { // use 2:2:1:1
                    repeat(2) {
                        scratchPatternsResult.add(pattern0)
                        scratchPatternsResult.add(pattern1)
                    }
                    repeat(1) {
                        scratchPatternsResult.add(pattern2)
                        scratchPatternsResult.add(pattern3)
                    }
                }

            }

            else -> throw IllegalStateException("impossible random patterns size: ${randomPatterns.size}")
        }

        return scratchPatternsResult.randomSort()
    }
}