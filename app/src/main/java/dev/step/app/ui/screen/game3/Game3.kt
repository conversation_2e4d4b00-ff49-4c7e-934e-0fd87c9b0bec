@file:Suppress("LocalVariableName")

package dev.step.app.ui.screen.game3

import android.annotation.SuppressLint
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.androidplatform.ext.navigationBarHeight
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.MyCoinsForGame3
import dev.step.app.ui.common.appluck.AppLuckItem
import dev.step.app.ui.common.appluck.AppLuckPlaceholder
import dev.step.app.ui.common.game.sc.ScratchCard
import dev.step.app.ui.common.game.sc.ScratchView
import dev.step.app.ui.screen.home.SystemBarsColor
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun Game3(
    navUp: () -> Unit,
    openNoRemainingTimeDialog: (gameADT: GameADT) -> Unit,
    openRewardedDialog: (gameADT: GameADT, coins: Int, times: Int) -> Unit,
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

//    val _navUp = {
//        navUp()
//        interstitialAdHelper.tryToShowAd("exit_game3")
//    }

    NavBackHandler(onBack = OnBack)

    Game3(
        navUp = OnBack,
        openNoRemainingTimeDialog = openNoRemainingTimeDialog,
        openRewardedDialog = openRewardedDialog,
        viewModel = koinViewModel()
    )

    SystemBarsColor.DisposableEffectSystemBarsColorUseDarkIcons(useDarkIcons = false)
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
private fun Game3(
    navUp: () -> Unit,
    openNoRemainingTimeDialog: (gameADT: GameADT) -> Unit,
    openRewardedDialog: (gameADT: GameADT, coins: Int, times: Int) -> Unit,
    viewModel: Game3ViewModel,
) {

    var scratchView by remember {
        mutableStateOf<ScratchView?>(null)
    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            Lifecycle.Event.ON_RESUME -> viewModel.onRefreshCoins()
            else -> {}
        }
    }

    viewModel.collectSideEffect {
        when (it) {
            is Game3SideEffect.ResetScratchCard -> {
                scratchView?.reset()
                viewModel.doSelectionScratchCard()
            }

            is Game3SideEffect.DisableScratchCard -> {
                scratchView?.reset()
                viewModel.disableScratchCard()
            }

            is Game3SideEffect.ToRewardedDialog -> {
                openRewardedDialog(
                    it.gameADT,
                    it.coins,
                    it.times
                )
            }

            is Game3SideEffect.ToNoRemainingTimeDialog -> openNoRemainingTimeDialog(it.gameADT)
        }
    }

    val viewState by viewModel.collectAsState()

    Scaffold { _ ->
        Box {
            Image(
                painter = painterResource(id = R.drawable.bg_game3),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.fillMaxSize()
            )
            Image(
                painter = painterResource(id = R.drawable.img_game3_light),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth()
            )
        }
        Column {
            Game3TopBar(walletCoins = viewState.walletCoins, navUp = navUp)

            Game3Content(
                viewState = viewState,
                onScratchCardLayout = { scratchView = it },
                onScratchCardTouch = viewModel::startScratch,
                onScratchCompleted = viewModel::onScratchCompleted,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
private fun Game3Content(
    viewState: Game3ViewState,
    onScratchCardLayout: (ScratchView) -> Unit,
    onScratchCardTouch: () -> Unit,
    onScratchCompleted: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp)
            ) {
                Image(
                    painter = painterResource(id = R.drawable.img_game3_title),
                    contentDescription = null,
                    modifier = Modifier.fillMaxWidth()
                )

                viewState.walletCoins?.let {
                    MyCoinsForGame3(
                        from = "game3",
                        coins = viewState.walletCoins,
                        onIncrease = { },
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(bottom = 43.dp)
                            .width(165.dp),
                        withdrawEnable = viewState.withdrawEnable,
                    )
                }
            }

            BlankSpacer(height = 10.dp)

            Column(
                modifier = Modifier.padding(horizontal = 8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val TEXT_GAME3_TIPS_0 = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            color = Color.White,
                            fontSize = 16.sp,
                            fontStyle = FontStyle.Italic,
                            fontWeight = FontWeight.SemiBold
                        )
                    ) {
                        append(stringResource(R.string.lucky_scratch_tips_0_0))
                    }

                    withStyle(
                        style = SpanStyle(
                            color = Color(0xFFFFE800),
                            fontSize = 16.sp,
                            fontStyle = FontStyle.Italic,
                            fontWeight = FontWeight.SemiBold,
                        )
                    ) {
                        append(stringResource(R.string.lucky_scratch_tips_0_1))
                    }
                }

                val TEXT_GAME3_TIPS_1 = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            color = Color.White,
                            fontSize = 14.sp,
                            fontFamily = FontFamily(Font(R.font.gabarito_regular))
                        )
                    ) {
                        append(stringResource(R.string.lucky_scratch_tips_1))
                    }
                }

                Text(
                    text = TEXT_GAME3_TIPS_0,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.scale(0.92f, 1f)
                )
                BlankSpacer(height = 3.dp)
                Text(
                    text = TEXT_GAME3_TIPS_1,
                    textAlign = TextAlign.Center,
                    fontFamily = FontFamily(Font(R.font.gabarito_regular)),
                    modifier = Modifier.scale(0.96f, 1f)
                )
            }

            BlankSpacer(height = 10.dp)

            ScratchCard(
                scratchEnable = viewState.scratchState != ScratchState.Disable,
                painters = viewState.scratchPatterns.map { painterResource(id = it.patternResId) },
                onLayout = onScratchCardLayout,
                onTouch = onScratchCardTouch,
                onScratchCompleted = onScratchCompleted,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp)
            )

            Spacer(modifier = Modifier.weight(1f))

            Text(
                text = stringResource(R.string.title_today_remaining_cards) + viewState.remaining.remainingTimes,
                color = Color.White,
                fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 17.sp else 21.sp,
                fontStyle = FontStyle.Italic,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.bodyWidth()
            )

            Box(
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 12.dp)
                    .fillMaxWidth()
            ) {
                AppLuckItem(
                    placeholder = AppLuckPlaceholder.NO_4,
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .padding(start = 20.dp)
                )
            }

            BlankSpacer(height = 20.dp)

            BlankSpacer(height = LocalContext.current.navigationBarHeight)
        }
    }
}

@Composable
private fun Game3TopBar(
    walletCoins: Int?,
    navUp: () -> Unit,
) {
    val context = LocalContext.current
    var coinIncrease by remember { mutableStateOf(false) }

    LaunchedEffect(coinIncrease) {
        if (coinIncrease) {
            delay(2500)
            coinIncrease = false
        }
    }

    Column {
        BlankSpacer(height = context.statusBarHeight)

        TopAppBar(
            title = {
                Box(
                    modifier = Modifier
                        .bodyWidth()
                        .padding(end = 16.dp)
                ) {
//                    walletCoins?.let { _ ->
//                        MyCoinsChip(
//                            from = "game3",
//                            coins = walletCoins,
//                            onIncrease = { coinIncrease = true },
//                            modifier = Modifier
//                                .align(Alignment.Center)
//                                .padding(horizontal = 16.dp),
//                            color = Color.White
//                        )
//                    }

                    if (coinIncrease) {
//                    CoinsAnimation(
//                        modifier = Modifier
//                            .size(60.dp)
//                            .padding(bottom = 8.dp, end = 3.dp)
//                            .align(Alignment.CenterEnd)
//                    )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.KeyboardArrowLeft,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = Color.White
                    )
                }
            },
            actions = {
                IconButton(onClick = navUp, enabled = false) {
                    Icon(
                        imageVector = Icons.Rounded.KeyboardArrowLeft,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = Color.Transparent
                    )
                }
            },
            backgroundColor = Color.Transparent,
            elevation = 0.dp
        )
    }


}


@Preview
@Composable
private fun Game3Preview() {
    Game3(
        {},
        {},
        { _, _, _ -> })

}