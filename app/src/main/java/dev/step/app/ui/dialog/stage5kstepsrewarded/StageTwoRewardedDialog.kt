package dev.step.app.ui.dialog.stage5kstepsrewarded

import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.ui.common.BigBadgeDialog
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.compose.collectSideEffect
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.viewmodel.container
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce

@Composable
fun StageTwoRewardedDialog(
    navUp: () -> Unit,
    remoteConfigHelper: FirebaseRemoteConfigHelper = koinInject(),
    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    viewModel: StageTwoRewardedDialogViewModel = koinViewModel(),
) {
    val viewState by viewModel.collectAsState()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    viewModel.collectSideEffect {
        when (it) {
            is StageTwoRewardedDialogSideEffect.NavUp -> navUp()
        }
    }

    val dailyTask = remoteConfigHelper.getDailyTask()

    val onClose = {
        val addCoins = dailyTask?.s2_rewarded_coins ?: 0

        viewModel.walletBizKv.setCoinBalance(
            viewModel.walletBizKv.getCoinBalance() + addCoins
        )

        OnBack()

        logEventRecord("exit_dialog_task2")
        debugLog("exit_dialog_task2")
    }

    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = onClose,
            onClose = onClose,
            onConfirm = {
                viewModel.doClickGetCoinsMultiply()
                rewardedAdHelper.tryToShowRewardedLoadingDialog("task2bonus")

                logEventRecord("click_task2_bonus")
            },
            confirmText = stringResource(R.string.title_get_coins),
            singleRewardTimes = dailyTask?.s2_rewarded_multiplier ?: 1,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_5k_steps),
            bigBadgeTitle = "+ ${dailyTask?.s2_rewarded_coins} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = "task2"
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 22.dp)

                Text(text = stringResource(R.string.title_completing_task_bonus), fontSize = 20.sp)

                BlankSpacer(height = 32.dp)
            }
        }
    }
}

sealed interface StageTwoRewardedDialogSideEffect {
    data object NavUp : StageTwoRewardedDialogSideEffect
}

data class StageTwoRewardedDialogViewState(
    val hasClickGetCoinsMultiply: Boolean = false
) {
    companion object {
        val Empty = StageTwoRewardedDialogViewState()
    }
}

@KoinViewModel
class StageTwoRewardedDialogViewModel(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val admobInterstitialAdManager: AdmobInterstitialAdManager,
    val walletBizKv: WalletBizKv,
) : ViewModel(), ContainerHost<StageTwoRewardedDialogViewState, StageTwoRewardedDialogSideEffect> {

    override val container: Container<StageTwoRewardedDialogViewState, StageTwoRewardedDialogSideEffect> =
        container(StageTwoRewardedDialogViewState.Empty)

    private val dailyTask = remoteConfigHelper.getDailyTask()

    init {

        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        globalMainActivity?.let { activity ->
            admobInterstitialAdManager.tryToLoadAd(activity)
        }

        viewModelScope.launch {
            val todayStartInstant = nowInstant().todayStartInstant()
            walletBizKv.doneStageTwoTask(todayStartInstant)
        }

        rewardedLoadingDialogFinishEventFlow.onEach {
            val addCoins =
                (dailyTask?.s2_rewarded_coins ?: 0) * (dailyTask?.s2_rewarded_multiplier ?: 1)

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            intent {
                postSideEffect(StageTwoRewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)
    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}
