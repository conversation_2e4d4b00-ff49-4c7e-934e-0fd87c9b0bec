@file:Suppress("LocalVariableName")

package dev.step.app.ui.screen.game2

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.Game2Node
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.androidplatform.ext.navigationBarHeight
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.MyCoinsChip
import dev.step.app.ui.common.appluck.AppLuckItem
import dev.step.app.ui.common.appluck.AppLuckPlaceholder
import dev.step.app.ui.common.game.sm.SlotMachine
import dev.step.app.ui.common.game.sm.SlotMachineView
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import dev.step.app.ui.theme.noRippleClickable
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun Game2(
    args: Game2Node.Game2Args,
    navUp: () -> Unit,
    openNoRemainingTimeDialog: (gameADT: GameADT) -> Unit,
    openRewardedDialog: (gameADT: GameADT, coins: Int, times: Int) -> Unit,
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()
    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

//    val _navUp = {
//        interstitialAdHelper.tryToShowAd(from = "exit_game2", onAdHiddenOrSkip = navUp)
//    }

    Game2(
        navUp = OnBack,
        openNoRemainingTimeDialog = openNoRemainingTimeDialog,
        openRewardedDialog = openRewardedDialog,
        viewModel = koinViewModel { parametersOf(args) }
    )
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
private fun Game2(
    navUp: () -> Unit,
    openNoRemainingTimeDialog: (gameADT: GameADT) -> Unit,
    openRewardedDialog: (gameADT: GameADT, coins: Int, times: Int) -> Unit,
    viewModel: Game2ViewModel
) {

    var slotMachineView by remember {
        mutableStateOf<SlotMachineView?>(null)
    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            Lifecycle.Event.ON_RESUME -> viewModel.onRefreshCoins()
            else -> {}
        }
    }

    viewModel.collectSideEffect {
        when (it) {
            is Game2SideEffect.DoPlay -> {
                slotMachineView?.apply {
                    debugLog("Game2 spin to ${it.selectionIndexes}")
                    var spinTo = ""

                    it.selectionIndexes.forEach { spinIndex ->
                        spinTo += spinIndex
                    }

                    start()
                    stop(spinTo)
                }
            }

            is Game2SideEffect.ToNoRemainingTimeDialog -> {
                openNoRemainingTimeDialog(it.gameADT)
            }

            is Game2SideEffect.ToRewardedDialog -> {
                openRewardedDialog(it.gameADT, it.coins, it.times)
            }
        }
    }

    val viewState by viewModel.collectAsState()

    NavBackHandler {
        if (!viewState.isPlaying) {
            navUp()
        }
    }

    Scaffold {
        Box(modifier = Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(id = R.drawable.bg_game2),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.fillMaxSize()
            )
        }

        Box {
            Game2TopBar(
                navUp = if (viewState.isPlaying) {
                    {}
                } else navUp
            )

            Image(
                painter = painterResource(id = R.drawable.img_game2_title),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .statusBarsPadding()
                    .padding(top = 48.dp)
            )
        }

        Game2Content(
            viewState = viewState,
            viewModel = viewModel,
            onSlotMachineLayout = { slotMachineView = it },
            onPlay = viewModel::onPlay,
            onPlayFinish = viewModel::onPlayFinish,
            modifier = Modifier
                .fillMaxSize()
                .systemBarsPadding()
        )
    }
}

@Composable
private fun Game2Content(
    viewState: Game2ViewState,
    viewModel: Game2ViewModel,
    onSlotMachineLayout: (SlotMachineView) -> Unit,
    onPlay: () -> Unit,
    onPlayFinish: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.weight(1f))

        var coinIncrease by remember { mutableStateOf(false) }

        LaunchedEffect(coinIncrease) {
            if (coinIncrease) {
                delay(2500)
                coinIncrease = false
            }
        }

        BlankSpacer(height = 138.dp)

        Box(modifier = Modifier.bodyWidth()) {
            viewState.walletCoins?.let { _ ->
                MyCoinsChip(
                    from = "game2",
                    coins = viewState.walletCoins,
                    onIncrease = { coinIncrease = true },
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(horizontal = 16.dp),
                    color = Color.White,
                    withdrawEnable = viewState.withdrawEnable
                )
            }

//                if (coinIncrease) {
//                    CoinsAnimation(
//                        modifier = Modifier
//                            .size(60.dp)
//                            .padding(bottom = 8.dp, end = 3.dp)
//                            .align(Alignment.CenterEnd)
//                    )
//                }
        }


        Box {
            Game2SlotMachine(
                isPlaying = viewState.isPlaying,
                onSlotMachineSpinsLayout = onSlotMachineLayout,
                onPlay = onPlay,
                onPlayFinish = onPlayFinish,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 18.dp)
            )

            Image(
                painter = painterResource(id = R.drawable.img_game2_coin_front_1),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 12.dp)
            )

            Image(
                painter = painterResource(id = R.drawable.img_game2_coin_front_2),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(end = 10.dp)
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        Text(
            text = stringResource(R.string.title_today_remaining_times) + viewState.remaining.remainingTimes,
            color = Color.White,
            fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 17.sp else 21.sp,
            fontStyle = FontStyle.Italic,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.bodyWidth()
        )

        Box(
            modifier = Modifier
                .padding(top = 8.dp, bottom = 22.dp)
                .fillMaxWidth()
        ) {
            AppLuckItem(
                placeholder = AppLuckPlaceholder.NO_3,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 20.dp)
            )
        }

        BlankSpacer(height = LocalContext.current.navigationBarHeight)
    }
}

@Composable
private fun Game2SlotMachine(
    isPlaying: Boolean,
    onSlotMachineSpinsLayout: (SlotMachineView) -> Unit,
    onPlay: () -> Unit,
    onPlayFinish: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Box(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .zIndex(1f)
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_sm_remaining_bg),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.Center)
                    .width(244.dp)
            )
        }

        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 20.dp)
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_sm_main),
                contentDescription = null,
                modifier = Modifier
                    .size(360.dp)
                    .align(Alignment.Center)
            )

            SlotMachine(
                onLayout = onSlotMachineSpinsLayout,
                onSlotMachineStop = onPlayFinish,
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth()
                    .padding(bottom = (49).dp)
            )

            PlayButton(
                clickable = !isPlaying,
                onClick = onPlay,
                modifier = Modifier
                    .bodyWidth()
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 12.dp)
            )
        }
    }
}

@Composable
private fun Game2TopBar(
    navUp: () -> Unit,
) {
    val context = LocalContext.current

    Column {
        BlankSpacer(height = context.statusBarHeight)

        TopAppBar(
            title = {},
            modifier = Modifier.fillMaxWidth(),
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.KeyboardArrowLeft,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = Color.White
                    )
                }
            },
            actions = {
                IconButton(onClick = navUp, enabled = false) {
                    Icon(
                        imageVector = Icons.Rounded.KeyboardArrowLeft,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = Color.Transparent
                    )
                }
            },
            elevation = 0.dp,
            backgroundColor = Color.Transparent
        )
    }
}

@Composable
private fun PlayButton(
    clickable: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.noRippleClickable {
        if (clickable) onClick()
    }) {
        Image(
            painter = painterResource(id = R.drawable.img_sm_play_button_bg),
            contentDescription = null
        )
        Text(
            text = stringResource(id = R.string.text_play),
            modifier = Modifier
                .align(Alignment.Center)
                .padding(bottom = if (Locale.current.language == "en") 5.dp else 8.dp),
            color = Color(0xFFFE5246),
            fontSize = if (Locale.current.language == "en") 29.sp else 23.sp
        )
    }
}

@Preview
@Composable
private fun SlotMachinePreview() {
    AppTheme {
        Game2SlotMachine(
            isPlaying = false,
            {},
            {},
            {},
            modifier = Modifier.fillMaxWidth()
        )
    }
}

//@Preview
//@Composable
//private fun Game2Preview() {
//    Game2(
//        {},
//        {},
//        { _, _, _ -> }
//    )
//}