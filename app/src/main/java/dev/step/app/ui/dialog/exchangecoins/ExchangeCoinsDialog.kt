@file:Suppress("LocalVariableName")

package dev.step.app.ui.dialog.exchangecoins

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.RatingHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.interstitial.MaxInterstitialAdHelper
import dev.step.app.androidplatform.biz.ad.rewarded.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.common.BigBadgeDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.dialog.rewardedloading.rewardedLoadingDialogTimeoutEventFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import org.orbitmvi.orbit.syntax.simple.intent
import org.orbitmvi.orbit.syntax.simple.postSideEffect
import org.orbitmvi.orbit.syntax.simple.reduce
import org.orbitmvi.orbit.viewmodel.container

@Composable
fun ExchangeCoinsDialog(
    navUp: () -> Unit,
    remoteConfigHelper: FirebaseRemoteConfigHelper = koinInject(),
    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    ratingHelper: RatingHelper = koinInject(),
    viewModel: ExchangeCoinsRewardedDialogViewModel = koinViewModel()
) {
    val stepExchange = remoteConfigHelper.getStepExchange()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    val addCoins = {
        val exchangeCoin = stepExchange?.exchange_coins_every_100_steps ?: 0

        viewModel.walletBizKv.setCoinBalance(
            viewModel.walletBizKv.getCoinBalance() + exchangeCoin
        )
    }

    val _navUp = {
        navUp()

        // cuz call navUp() happens before DisposableEffect { onDispose() }
        runCatching {
            ratingHelper.tryToOpenReviewDialog()
        }.onFailure {
            Firebase.crashlytics.recordException(it)
        }
        Unit
    }

    val viewState by viewModel.collectAsState()

    viewModel.collectSideEffect {
        when (it) {
            ExchangeCoinsRewardedDialogSideEffect.NavUp -> _navUp()
        }
    }

    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = {
//                interstitialAdHelper.tryToShowAd("exit_dialog_exchange")
                addCoins()
                OnBack()
            },
            onClose = {
//                interstitialAdHelper.tryToShowAd("exit_dialog_exchange")
                addCoins()
                OnBack()
            },
            onConfirm = {
                viewModel.doClickGetCoinsMultiply()

                rewardedAdHelper.tryToShowRewardedLoadingDialog("exchangebonus")
                logEventRecord("click_exchange_button")
            },
            confirmText = stringResource(id = R.string.title_get_coins),
            singleRewardTimes = stepExchange?.exchange_coins_multiplier ?: 1,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_exchange),
            bigBadgeTitle = "+ ${stepExchange?.exchange_coins_every_100_steps} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = "exchange",
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 22.dp)

                Text(text = stringResource(id = R.string.title_exchange_coins), fontSize = 20.sp)

                BlankSpacer(height = 32.dp)
            }
        }
    }
}


sealed interface ExchangeCoinsRewardedDialogSideEffect {
    data object NavUp : ExchangeCoinsRewardedDialogSideEffect
}

data class ExchangeCoinsRewardedDialogViewState(
    val hasClickGetCoinsMultiply: Boolean = false
) {
    companion object {
        val Empty = ExchangeCoinsRewardedDialogViewState()
    }
}

@KoinViewModel
class ExchangeCoinsRewardedDialogViewModel(
    interstitialAdHelper: MaxInterstitialAdHelper,
    remoteConfigHelper: FirebaseRemoteConfigHelper,
    val walletBizKv: WalletBizKv,
) : ViewModel(),
    ContainerHost<ExchangeCoinsRewardedDialogViewState, ExchangeCoinsRewardedDialogSideEffect> {

    override val container: Container<ExchangeCoinsRewardedDialogViewState, ExchangeCoinsRewardedDialogSideEffect> =
        container(ExchangeCoinsRewardedDialogViewState.Empty)

    init {
        rewardedLoadingDialogTimeoutEventFlow.onEach {
            doClickGetCoinsMultiply(false)
        }.launchIn(viewModelScope)

        interstitialAdHelper.tryToLoadAd()

        val stepExchange = remoteConfigHelper.getStepExchange()

        rewardedLoadingDialogFinishEventFlow.onEach {
            val exchangeCoin = stepExchange?.exchange_coins_every_100_steps ?: 0
            val times = stepExchange?.exchange_coins_multiplier ?: 1

            val addCoins = exchangeCoin * times

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + addCoins
            )

            intent {
                postSideEffect(ExchangeCoinsRewardedDialogSideEffect.NavUp)
            }
        }.launchIn(viewModelScope)

//        interstitialAdHelper.interstitialAdEventFlow.onEach { adShowEvent ->
//            if (adShowEvent is MaxInterstitialAdShowEvent.Skip || adShowEvent is MaxInterstitialAdShowEvent.Showing) {
//
//                val exchangeCoin = stepExchange?.exchange_coins_every_100_steps ?: 0
//
//                walletBizKv.setCoinBalance(
//                    walletBizKv.getCoinBalance() + exchangeCoin
//                )
//
//                intent {
//                    postSideEffect(ExchangeCoinsRewardedDialogSideEffect.NavUp)
//                }
//                logEventRecord("exit_dialog_exchange")
//            }
//        }.launchIn(viewModelScope)
    }

    fun doClickGetCoinsMultiply(clicked: Boolean = true) = intent {
        reduce {
            state.copy(hasClickGetCoinsMultiply = clicked)
        }
    }
}