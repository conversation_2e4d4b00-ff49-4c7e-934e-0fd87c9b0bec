package dev.step.app.ui.dialog.motionsensorsensitivitychange

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ad.admob.interstitial.interstitialAdRegister
//import dev.step.app.androidplatform.biz.MaxInterstitialAdHelper
import dev.step.app.data.adt.MotionSensorSensitivity
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.AppDefDialog
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.bodyWidth
import org.koin.compose.koinInject
import kotlin.math.roundToInt

@Composable
fun MotionSensorSensitivityChangeDialog(
    navUp: () -> Unit,
    userSettingsDataKv: UserSettingsDataKv = koinInject(),
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator)

    val scope = rememberCoroutineScope()

    var sensitivity by remember {
        mutableStateOf<MotionSensorSensitivity?>(null)
    }

    var sensitivityFloatValue by remember { mutableFloatStateOf(0f) }

    LaunchedEffect(Unit) {
        sensitivity = userSettingsDataKv.motionSensorSensitivity
        sensitivityFloatValue = (sensitivity ?: MotionSensorSensitivity.Lv1).sid.toFloat()
    }

    AppDefDialog(
        onDismiss = navUp,
        onCancel = navUp,
        onConfirm = {
            sensitivity?.let {
                userSettingsDataKv.changeMotionSensorSensitivity(it)
            }
//            interstitialAdHelper.tryToShowAd(
//                "save_motion_sensor_sensitivity",
//                onAdShowingOrSkip = { navUp() }
//            )
            OnBack()
        },
    ) {
        Column(
            modifier = Modifier
                .bodyWidth()
                .padding(horizontal = 16.dp)
                .padding(bottom = 28.dp), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = dev.step.app.R.string.text_sensitivity),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            BlankSpacer(height = 10.dp)
            Text(
                text = stringResource(id = R.string.sensitivity_setting_tips),
                color = AppColor.TextColorGray,
                fontSize = 14.sp,
                textAlign = TextAlign.Center
            )
            BlankSpacer(height = 6.dp)

            Slider(
                value = sensitivityFloatValue,
                onValueChange = {

                    debugLog("Slider onValueChange -> $it")
                    sensitivityFloatValue = it

                    val sid = it.roundToInt()
                    MotionSensorSensitivity.valueOf(sid)?.let { mss ->
                        sensitivity = mss
                    }
                },
                valueRange = 0f..3f,
                steps = 2,
                modifier = Modifier.padding(horizontal = 32.dp),
                colors = SliderDefaults.colors(
                    inactiveTrackColor = Color.LightGray,
                    inactiveTickColor = Color.Gray
                )
            )

            Row(
                modifier = Modifier.padding(horizontal = 26.dp)
            ) {
                Text(text = stringResource(id = dev.step.app.R.string.text_low))
                Spacer(modifier = Modifier.weight(1f))
                Text(text = stringResource(id = dev.step.app.R.string.text_medium))
                Spacer(modifier = Modifier.weight(1f))
                Text(text = stringResource(id = dev.step.app.R.string.text_high))
            }
        }
    }
}

@Preview
@Composable
fun MotionSensorSensitivityAdjustmentDialogPreview() {
    MotionSensorSensitivityChangeDialog(navUp = { })
}
